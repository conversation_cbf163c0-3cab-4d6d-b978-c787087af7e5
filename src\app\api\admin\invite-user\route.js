import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

import { createAdminClient } from '../../../../utils/supabase/server';

/**
 * API endpoint để invite user vào Supabase project
 * Chỉ admin với TOKEN_ACCESS_API hợp lệ mới có thể sử dụng
 * @param {Request} request - Request object với body: { email, name, role }
 * @returns {Promise<Response>} - Response object
 */
export async function POST(request) {
  try {
    // Kiểm tra xác thực TOKEN_ACCESS_API
    const headersList = await headers();
    const authHeader = headersList.get('authorization');
    const tokenAccessApi = process.env.TOKEN_ACCESS_API;

    if (!tokenAccessApi) {
      console.error('❌ TOKEN_ACCESS_API not configured in environment variables');
      return NextResponse.json({
        success: false,
        error: 'Server configuration error - TOKEN_ACCESS_API not configured',
        code: 'CONFIG_ERROR'
      }, { status: 500 });
    }

    // Ki<PERSON>m tra Authorization header
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: 'Missing or invalid Authorization header. Use: Bearer <TOKEN_ACCESS_API>',
        code: 'UNAUTHORIZED'
      }, { status: 401 });
    }

    const providedToken = authHeader.replace('Bearer ', '');

    // Xác thực token
    if (providedToken !== tokenAccessApi) {
      console.warn('🚫 Invalid TOKEN_ACCESS_API attempt:', {
        providedToken: providedToken.substring(0, 8) + '...',
        timestamp: new Date().toISOString(),
        ip: headersList.get('x-forwarded-for') || 'unknown'
      });

      return NextResponse.json({
        success: false,
        error: 'Invalid access token',
        code: 'INVALID_TOKEN'
      }, { status: 403 });
    }

    // Parse request body
    const { email, name, phone } = await request.json();

    // Validate input data
    if (!email || !name) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: email and name are required',
        code: 'VALIDATION_ERROR'
      }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email format',
        code: 'VALIDATION_ERROR'
      }, { status: 400 });
    }

    // Validate phone format (optional but if provided must be valid)
    if (phone) {
      // Vietnamese phone number format: +84xxxxxxxxx or 0xxxxxxxxx
      const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
      const cleanPhone = phone.replace(/[\s\-\(\)]/g, ''); // Remove spaces, dashes, parentheses

      if (!phoneRegex.test(cleanPhone)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid phone format. Use Vietnamese format: +84xxxxxxxxx or 0xxxxxxxxx',
          code: 'VALIDATION_ERROR'
        }, { status: 400 });
      }
    }

    console.log('🔐 Admin invite user request:', {
      email: email.replace(/(.{2}).*(@.*)/, '$1***$2'), // Mask email for security
      name,
      phone: phone ? phone.replace(/(.{3}).*(.{3})/, '$1***$2') : null, // Mask phone for security
      timestamp: new Date().toISOString()
    });

    // Tạo admin client với service role
    const supabase = createAdminClient();

    // Normalize phone number for storage
    let normalizedPhone = null;
    if (phone) {
      const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
      // Convert to international format +84
      if (cleanPhone.startsWith('0')) {
        normalizedPhone = '+84' + cleanPhone.substring(1);
      } else if (cleanPhone.startsWith('84')) {
        normalizedPhone = '+' + cleanPhone;
      } else if (cleanPhone.startsWith('+84')) {
        normalizedPhone = cleanPhone;
      } else {
        normalizedPhone = '+84' + cleanPhone;
      }
    }

    // Invite user using Supabase Auth Admin API
    // Trigger sẽ tự động tạo tenant mới và set user làm tenant owner
    const { data: inviteData, error: inviteError } = await supabase.auth.admin.inviteUserByEmail(email, {
      data: {
        full_name: name,
        name: name,
        phone: normalizedPhone,
        invited_by: 'admin',
        invited_at: new Date().toISOString()
      },
      redirectTo: `${process.env.NEXT_PUBLIC_SERVER_URL}/auth/callback`
    });

    if (inviteError) {
      console.error('❌ Supabase invite error:', inviteError);

      // Handle specific Supabase errors
      if (inviteError.message?.includes('User already registered')) {
        return NextResponse.json({
          success: false,
          error: 'User with this email already exists',
          code: 'USER_EXISTS'
        }, { status: 409 });
      }

      return NextResponse.json({
        success: false,
        error: inviteError.message || 'Failed to invite user',
        code: 'INVITE_ERROR'
      }, { status: 500 });
    }

    // Log successful invite
    console.log('✅ User invited successfully:', {
      userId: inviteData.user?.id,
      email: email.replace(/(.{2}).*(@.*)/, '$1***$2'),
      phone: normalizedPhone ? normalizedPhone.replace(/(.{3}).*(.{3})/, '$1***$2') : null,
      timestamp: new Date().toISOString()
    });

    // Note: Trigger handle_new_user() sẽ tự động:
    // - Tạo tenant mới cho user
    // - Tạo user record trong public.users với is_tenant_owner = true
    // - Cấp 200 credits welcome
    // - Set user metadata với tenant_id

    return NextResponse.json({
      success: true,
      message: 'User invited successfully. Tenant will be auto-created on first login.',
      data: {
        user_id: inviteData.user?.id,
        email: email,
        name: name,
        phone: normalizedPhone,
        invited_at: new Date().toISOString(),
        confirmation_sent_at: inviteData.user?.confirmation_sent_at,
        note: 'User will become tenant owner with 200 welcome credits'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('💥 Unexpected error in invite-user API:', error);

    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET endpoint để kiểm tra trạng thái API
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Invite User API is running',
    endpoint: '/api/admin/invite-user',
    method: 'POST',
    required_headers: {
      'Authorization': 'Bearer <TOKEN_ACCESS_API>',
      'Content-Type': 'application/json'
    },
    required_body: {
      email: '<EMAIL>',
      name: 'User Name',
      phone: '+84901234567 (optional, Vietnamese format)'
    },
    phone_formats: [
      '+84xxxxxxxxx (international format)',
      '0xxxxxxxxx (local format)',
      'Auto-normalized to +84 format'
    ],
    note: 'User will automatically become tenant owner with new tenant and 200 welcome credits'
  });
}